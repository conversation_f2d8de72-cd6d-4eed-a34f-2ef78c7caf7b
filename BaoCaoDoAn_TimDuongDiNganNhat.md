# BÁO CÁO ĐỒ ÁN
## TÌM ĐƯỜNG ĐI NGẮN NHẤT TRÊN ĐỒ THỊ CÓ TRỌNG SỐ

---

## MỤC LỤC

**LỜI CẢM ƠN**

**CHƯƠNG 1: TỔNG QUAN ĐỀ TÀI**
- 1.1 Giới thiệu chung
- 1.2 Đặt vấn đề  
- 1.3 M<PERSON>c tiêu và phạm vi đề tài

**CHƯƠNG 2: NGHIÊN CỨU LÝ THUYẾT**
- 2.1 Kh<PERSON>i niệm cơ bản về đồ thị
- 2.2 Bài toán đường đi ngắn nhất
- 2.3 <PERSON><PERSON><PERSON> thuật toán tìm đường đi ngắn nhất
  - 2.3.1 Thuật to<PERSON>stra
  - 2.3.2 Thu<PERSON><PERSON> to<PERSON>-Ford
  - 2.3.3 Thuật toán Floyd-Warshall
  - 2.3.4 Thu<PERSON>t toán SPFA (Shortest Path Faster Algorithm)

**CHƯƠNG 3: XÂY DỰNG CHƯƠNG TRÌNH THUẬT TOÁN**
- 3.1 Lựa chọn thuật toán
- 3.2 Cấu trú<PERSON> dữ liệu sử dụng
- 3.3 Chi tiết cài đặt thuật toán
- 3.4 Giao diện chương trình và hướng dẫn sử dụng
- 3.5 <PERSON><PERSON><PERSON> thử và đánh giá

**CHƯƠNG 4: KẾT QUẢ VÀ HƯỚNG PHÁT TRIỂN**
- 4.1 Kết quả đạt được
- 4.2 Hạn chế của đề tài
- 4.3 Hướng phát triển

**KẾT LUẬN**

**TÀI LIỆU THAM KHẢO**

**PHỤ LỤC**

---

## LỜI CẢM ƠN

Em xin chân thành cảm ơn thầy/cô giáo đã tận tình hướng dẫn và giúp đỡ em trong quá trình thực hiện đồ án này. Qua đồ án, em đã có cơ hội tìm hiểu sâu hơn về các thuật toán tìm đường đi ngắn nhất trên đồ thị và ứng dụng thực tế của chúng.

Em cũng xin cảm ơn gia đình, bạn bè đã động viên và hỗ trợ em trong suốt quá trình học tập và nghiên cứu.

---

## CHƯƠNG 1: TỔNG QUAN ĐỀ TÀI

### 1.1 Giới thiệu chung

Bài toán tìm đường đi ngắn nhất trên đồ thị là một trong những bài toán cơ bản và quan trọng nhất trong lý thuyết đồ thị và khoa học máy tính. Bài toán này có ứng dụng rộng rãi trong nhiều lĩnh vực như:

- **Giao thông vận tải**: Tìm tuyến đường ngắn nhất giữa hai địa điểm
- **Mạng máy tính**: Định tuyến gói tin trong mạng
- **Logistics**: Tối ưu hóa vận chuyển hàng hóa
- **Game development**: AI pathfinding
- **Kinh tế**: Tối ưu hóa chi phí trong chuỗi cung ứng

### 1.2 Đặt vấn đề

Trong thực tế, chúng ta thường gặp phải các bài toán cần tìm đường đi tối ưu giữa các điểm. Ví dụ:

- Một người muốn đi từ nhà đến công ty bằng đường ngắn nhất
- Một công ty vận chuyển muốn tối ưu hóa chi phí giao hàng
- Một hệ thống GPS cần tính toán tuyến đường tối ưu

Những bài toán này có thể được mô hình hóa thành bài toán tìm đường đi ngắn nhất trên đồ thị có trọng số, trong đó:
- Các đỉnh đại diện cho các địa điểm
- Các cạnh đại diện cho các con đường kết nối
- Trọng số của cạnh đại diện cho khoảng cách, thời gian hoặc chi phí

### 1.3 Mục tiêu và phạm vi đề tài

**Mục tiêu:**
- Nghiên cứu và hiểu rõ các thuật toán tìm đường đi ngắn nhất cơ bản
- Cài đặt và minh họa hoạt động của các thuật toán bằng ngôn ngữ Python
- So sánh hiệu quả và ứng dụng của từng thuật toán
- Xây dựng chương trình demo có giao diện trực quan

**Phạm vi đề tài:**
- Tập trung vào 4 thuật toán chính: Dijkstra, Bellman-Ford, Floyd-Warshall, SPFA
- Cài đặt trên đồ thị có hướng và vô hướng
- Xử lý đồ thị có trọng số dương và âm
- Xây dựng giao diện đơn giản để minh họa

---

## CHƯƠNG 2: NGHIÊN CỨU LÝ THUYẾT

### 2.1 Khái niệm cơ bản về đồ thị

**Định nghĩa đồ thị:**
Đồ thị G = (V, E) bao gồm:
- V: Tập hợp các đỉnh (vertices)
- E: Tập hợp các cạnh (edges)

**Đồ thị có trọng số:**
Là đồ thị mà mỗi cạnh được gán một giá trị số gọi là trọng số, thường ký hiệu là w(u,v) cho cạnh từ đỉnh u đến đỉnh v.

**Các khái niệm liên quan:**
- **Đường đi (Path)**: Dãy các đỉnh liên tiếp được nối với nhau bởi các cạnh
- **Độ dài đường đi**: Tổng trọng số của các cạnh trên đường đi
- **Đường đi ngắn nhất**: Đường đi có độ dài nhỏ nhất giữa hai đỉnh

### 2.2 Bài toán đường đi ngắn nhất

**Phát biểu bài toán:**
Cho đồ thị có trọng số G = (V, E) và hai đỉnh s, t ∈ V. Tìm đường đi từ s đến t có tổng trọng số nhỏ nhất.

**Các biến thể của bài toán:**
1. **Single-source shortest path**: Tìm đường đi ngắn nhất từ một đỉnh nguồn đến tất cả các đỉnh khác
2. **Single-destination shortest path**: Tìm đường đi ngắn nhất từ tất cả các đỉnh đến một đỉnh đích
3. **Single-pair shortest path**: Tìm đường đi ngắn nhất giữa hai đỉnh cụ thể
4. **All-pairs shortest path**: Tìm đường đi ngắn nhất giữa mọi cặp đỉnh

### 2.3 Các thuật toán tìm đường đi ngắn nhất

#### 2.3.1 Thuật toán Dijkstra

**Ý tưởng:**
Thuật toán Dijkstra sử dụng phương pháp tham lam (greedy) để tìm đường đi ngắn nhất từ một đỉnh nguồn đến tất cả các đỉnh khác trong đồ thị có trọng số không âm.

**Nguyên lý hoạt động:**
1. Khởi tạo khoảng cách từ đỉnh nguồn đến chính nó bằng 0, các đỉnh khác bằng vô cùng
2. Chọn đỉnh chưa được xử lý có khoảng cách nhỏ nhất
3. Cập nhật khoảng cách đến các đỉnh kề
4. Đánh dấu đỉnh đã được xử lý
5. Lặp lại cho đến khi xử lý hết tất cả đỉnh

**Độ phức tạp:**
- Thời gian: O(V²) với mảng, O((V + E)logV) với heap
- Không gian: O(V)

**Ưu điểm:**
- Hiệu quả với đồ thị có trọng số không âm
- Dễ hiểu và cài đặt
- Cho kết quả tối ưu

**Nhược điểm:**
- Không xử lý được trọng số âm
- Phải xử lý toàn bộ đồ thị ngay cả khi chỉ cần tìm đường đi đến một đỉnh cụ thể

#### 2.3.2 Thuật toán Bellman-Ford

**Ý tưởng:**
Thuật toán Bellman-Ford có thể xử lý đồ thị có trọng số âm và phát hiện chu trình âm.

**Nguyên lý hoạt động:**
1. Khởi tạo khoảng cách từ đỉnh nguồn đến chính nó bằng 0, các đỉnh khác bằng vô cùng
2. Lặp V-1 lần, mỗi lần duyệt qua tất cả các cạnh và thực hiện phép relaxation
3. Kiểm tra chu trình âm bằng cách thực hiện thêm một lần nữa phép relaxation

**Độ phức tạp:**
- Thời gian: O(VE)
- Không gian: O(V)

**Ưu điểm:**
- Xử lý được trọng số âm
- Phát hiện được chu trình âm
- Đơn giản để cài đặt

**Nhược điểm:**
- Chậm hơn Dijkstra với đồ thị có trọng số không âm
- Độ phức tạp thời gian cao

#### 2.3.3 Thuật toán Floyd-Warshall

**Ý tưởng:**
Thuật toán Floyd-Warshall tìm đường đi ngắn nhất giữa mọi cặp đỉnh trong đồ thị.

**Nguyên lý hoạt động:**
Sử dụng quy hoạch động với ý tưởng: đường đi ngắn nhất từ i đến j có thể đi qua đỉnh trung gian k hoặc không.

**Công thức:**
```
dist[i][j] = min(dist[i][j], dist[i][k] + dist[k][j])
```

**Độ phức tạp:**
- Thời gian: O(V³)
- Không gian: O(V²)

**Ưu điểm:**
- Tìm được đường đi ngắn nhất giữa mọi cặp đỉnh
- Xử lý được trọng số âm
- Cài đặt đơn giản

**Nhược điểm:**
- Độ phức tạp thời gian và không gian cao
- Không hiệu quả với đồ thị lớn

#### 2.3.4 Thuật toán SPFA (Shortest Path Faster Algorithm)

**Ý tưởng:**
SPFA là cải tiến của thuật toán Bellman-Ford, sử dụng hàng đợi để tối ưu hóa quá trình relaxation.

**Nguyên lý hoạt động:**
1. Sử dụng hàng đợi để lưu các đỉnh cần được xử lý
2. Chỉ thực hiện relaxation khi cần thiết
3. Kiểm tra chu trình âm bằng cách đếm số lần một đỉnh được đưa vào hàng đợi

**Độ phức tạp:**
- Thời gian: O(VE) trong trường hợp xấu nhất, O(E) trung bình
- Không gian: O(V)

**Ưu điểm:**
- Nhanh hơn Bellman-Ford trong thực tế
- Xử lý được trọng số âm
- Phát hiện được chu trình âm

**Nhược điểm:**
- Vẫn có thể chậm trong trường hợp xấu nhất
- Phức tạp hơn Dijkstra

---

## CHƯƠNG 3: XÂY DỰNG CHƯƠNG TRÌNH THUẬT TOÁN

### 3.1 Lựa chọn thuật toán

Trong đồ án này, em chọn cài đặt thuật toán **Dijkstra** làm thuật toán chính vì những lý do sau:

1. **Tính phổ biến**: Dijkstra là thuật toán được sử dụng rộng rãi nhất trong thực tế
2. **Hiệu quả**: Có độ phức tạp thời gian tốt với đồ thị có trọng số không âm
3. **Dễ hiểu**: Thuật toán có logic rõ ràng, dễ cài đặt và debug
4. **Ứng dụng thực tế**: Được sử dụng trong nhiều hệ thống định tuyến thực tế

Ngoài ra, em cũng cài đặt thêm thuật toán **Bellman-Ford** để so sánh và minh họa khả năng xử lý trọng số âm.

### 3.2 Cấu trúc dữ liệu sử dụng

**1. Biểu diễn đồ thị:**
- Sử dụng **danh sách kề (Adjacency List)** để biểu diễn đồ thị
- Mỗi đỉnh được lưu dưới dạng dictionary với key là đỉnh đích và value là trọng số

**2. Cấu trúc dữ liệu hỗ trợ:**
- **Priority Queue (heapq)**: Để chọn đỉnh có khoảng cách nhỏ nhất trong Dijkstra
- **List**: Để lưu khoảng cách và đường đi
- **Set**: Để đánh dấu các đỉnh đã được xử lý

### 3.3 Chi tiết cài đặt thuật toán

**Cấu trúc lớp Graph:**

```python
import heapq
import sys
from collections import defaultdict, deque
import matplotlib.pyplot as plt
import networkx as nx

class Graph:
    def __init__(self):
        self.vertices = set()
        self.edges = defaultdict(list)

    def add_edge(self, u, v, weight):
        """Thêm cạnh có trọng số vào đồ thị"""
        self.vertices.add(u)
        self.vertices.add(v)
        self.edges[u].append((v, weight))

    def add_undirected_edge(self, u, v, weight):
        """Thêm cạnh vô hướng"""
        self.add_edge(u, v, weight)
        self.add_edge(v, u, weight)
```

**Cài đặt thuật toán Dijkstra:**

```python
def dijkstra(self, start, end=None):
    """
    Thuật toán Dijkstra tìm đường đi ngắn nhất
    Args:
        start: Đỉnh bắt đầu
        end: Đỉnh kết thúc (None nếu tìm đến tất cả đỉnh)
    Returns:
        distances: Dictionary chứa khoảng cách ngắn nhất
        previous: Dictionary chứa đỉnh trước đó trong đường đi ngắn nhất
    """
    # Khởi tạo
    distances = {vertex: float('infinity') for vertex in self.vertices}
    previous = {vertex: None for vertex in self.vertices}
    distances[start] = 0

    # Priority queue: (distance, vertex)
    pq = [(0, start)]
    visited = set()

    while pq:
        current_distance, current_vertex = heapq.heappop(pq)

        # Nếu đã thăm đỉnh này thì bỏ qua
        if current_vertex in visited:
            continue

        visited.add(current_vertex)

        # Nếu tìm được đỉnh đích thì dừng
        if end and current_vertex == end:
            break

        # Duyệt các đỉnh kề
        for neighbor, weight in self.edges[current_vertex]:
            if neighbor not in visited:
                new_distance = current_distance + weight

                # Relaxation
                if new_distance < distances[neighbor]:
                    distances[neighbor] = new_distance
                    previous[neighbor] = current_vertex
                    heapq.heappush(pq, (new_distance, neighbor))

    return distances, previous
```

**Cài đặt thuật toán Bellman-Ford:**

```python
def bellman_ford(self, start):
    """
    Thuật toán Bellman-Ford
    Returns:
        distances: Dictionary chứa khoảng cách ngắn nhất
        previous: Dictionary chứa đỉnh trước đó
        has_negative_cycle: Boolean cho biết có chu trình âm hay không
    """
    # Khởi tạo
    distances = {vertex: float('infinity') for vertex in self.vertices}
    previous = {vertex: None for vertex in self.vertices}
    distances[start] = 0

    # Relaxation V-1 lần
    for _ in range(len(self.vertices) - 1):
        for u in self.vertices:
            for v, weight in self.edges[u]:
                if distances[u] != float('infinity') and distances[u] + weight < distances[v]:
                    distances[v] = distances[u] + weight
                    previous[v] = u

    # Kiểm tra chu trình âm
    has_negative_cycle = False
    for u in self.vertices:
        for v, weight in self.edges[u]:
            if distances[u] != float('infinity') and distances[u] + weight < distances[v]:
                has_negative_cycle = True
                break
        if has_negative_cycle:
            break

    return distances, previous, has_negative_cycle
```

**Hàm tái tạo đường đi:**

```python
def get_path(self, previous, start, end):
    """Tái tạo đường đi từ start đến end"""
    path = []
    current = end

    while current is not None:
        path.append(current)
        current = previous[current]

    path.reverse()

    # Kiểm tra xem có đường đi hay không
    if path[0] != start:
        return None

    return path
```

### 3.4 Giao diện chương trình và hướng dẫn sử dụng

**Lớp ShortestPathVisualizer:**

```python
class ShortestPathVisualizer:
    def __init__(self):
        self.graph = Graph()

    def create_sample_graph(self):
        """Tạo đồ thị mẫu để demo"""
        # Thêm các cạnh vào đồ thị
        edges = [
            ('A', 'B', 4), ('A', 'C', 2),
            ('B', 'C', 1), ('B', 'D', 5),
            ('C', 'D', 8), ('C', 'E', 10),
            ('D', 'E', 2), ('D', 'F', 6),
            ('E', 'F', 3)
        ]

        for u, v, w in edges:
            self.graph.add_undirected_edge(u, v, w)

    def visualize_graph(self, path=None, title="Đồ thị"):
        """Vẽ đồ thị bằng matplotlib và networkx"""
        G = nx.Graph()

        # Thêm các cạnh vào NetworkX graph
        for u in self.graph.vertices:
            for v, weight in self.graph.edges[u]:
                if not G.has_edge(u, v):  # Tránh thêm cạnh trùng lặp
                    G.add_edge(u, v, weight=weight)

        plt.figure(figsize=(12, 8))
        pos = nx.spring_layout(G, seed=42)

        # Vẽ tất cả các cạnh
        nx.draw_networkx_edges(G, pos, alpha=0.5, width=1)

        # Vẽ đường đi ngắn nhất nếu có
        if path:
            path_edges = [(path[i], path[i+1]) for i in range(len(path)-1)]
            nx.draw_networkx_edges(G, pos, edgelist=path_edges,
                                 edge_color='red', width=3)

        # Vẽ các đỉnh
        nx.draw_networkx_nodes(G, pos, node_color='lightblue',
                              node_size=1000, alpha=0.9)

        # Vẽ nhãn đỉnh
        nx.draw_networkx_labels(G, pos, font_size=16, font_weight='bold')

        # Vẽ trọng số cạnh
        edge_labels = nx.get_edge_attributes(G, 'weight')
        nx.draw_networkx_edge_labels(G, pos, edge_labels, font_size=12)

        plt.title(title, fontsize=16, fontweight='bold')
        plt.axis('off')
        plt.tight_layout()
        plt.show()

    def run_demo(self):
        """Chạy demo chương trình"""
        print("=== DEMO THUẬT TOÁN TÌM ĐƯỜNG ĐI NGẮN NHẤT ===\n")

        # Tạo đồ thị mẫu
        self.create_sample_graph()

        print("Đồ thị mẫu đã được tạo với các đỉnh:",
              sorted(list(self.graph.vertices)))
        print("\nCác cạnh trong đồ thị:")
        for u in sorted(self.graph.vertices):
            for v, weight in self.graph.edges[u]:
                if u < v:  # Chỉ in mỗi cạnh một lần
                    print(f"  {u} -- {v}: {weight}")

        # Hiển thị đồ thị
        self.visualize_graph(title="Đồ thị ban đầu")

        # Nhập đỉnh bắt đầu và kết thúc
        start = input("\nNhập đỉnh bắt đầu: ").strip().upper()
        end = input("Nhập đỉnh kết thúc: ").strip().upper()

        if start not in self.graph.vertices or end not in self.graph.vertices:
            print("Đỉnh không tồn tại trong đồ thị!")
            return

        # Chạy thuật toán Dijkstra
        print(f"\n=== THUẬT TOÁN DIJKSTRA ===")
        distances, previous = self.graph.dijkstra(start, end)
        path = self.graph.get_path(previous, start, end)

        if path:
            print(f"Đường đi ngắn nhất từ {start} đến {end}: {' -> '.join(path)}")
            print(f"Độ dài đường đi: {distances[end]}")

            # Hiển thị đồ thị với đường đi ngắn nhất
            self.visualize_graph(path, f"Đường đi ngắn nhất từ {start} đến {end} (Dijkstra)")
        else:
            print(f"Không có đường đi từ {start} đến {end}")

        # So sánh với Bellman-Ford
        print(f"\n=== THUẬT TOÁN BELLMAN-FORD ===")
        bf_distances, bf_previous, has_negative_cycle = self.graph.bellman_ford(start)

        if has_negative_cycle:
            print("Phát hiện chu trình âm trong đồ thị!")
        else:
            bf_path = self.graph.get_path(bf_previous, start, end)
            if bf_path:
                print(f"Đường đi ngắn nhất từ {start} đến {end}: {' -> '.join(bf_path)}")
                print(f"Độ dài đường đi: {bf_distances[end]}")

                # So sánh kết quả
                if distances[end] == bf_distances[end]:
                    print("✓ Kết quả của hai thuật toán giống nhau")
                else:
                    print("✗ Kết quả của hai thuật toán khác nhau")

# Hàm main để chạy chương trình
if __name__ == "__main__":
    visualizer = ShortestPathVisualizer()
    visualizer.run_demo()
```

**Hướng dẫn sử dụng:**

1. **Cài đặt thư viện cần thiết:**
   ```bash
   pip install matplotlib networkx
   ```

2. **Chạy chương trình:**
   ```bash
   python shortest_path.py
   ```

3. **Sử dụng chương trình:**
   - Chương trình sẽ tạo một đồ thị mẫu và hiển thị
   - Nhập đỉnh bắt đầu và đỉnh kết thúc
   - Chương trình sẽ tìm và hiển thị đường đi ngắn nhất
   - So sánh kết quả giữa thuật toán Dijkstra và Bellman-Ford

### 3.5 Kiểm thử và đánh giá

**Test case 1: Đồ thị cơ bản**
```
Đỉnh: A, B, C, D, E, F
Cạnh: A-B(4), A-C(2), B-C(1), B-D(5), C-D(8), C-E(10), D-E(2), D-F(6), E-F(3)
Tìm đường đi từ A đến F
Kết quả mong đợi: A -> C -> B -> D -> E -> F (độ dài: 12)
```

**Test case 2: Đồ thị có trọng số âm**
```python
def test_negative_weights():
    graph = Graph()
    graph.add_edge('A', 'B', 1)
    graph.add_edge('A', 'C', 4)
    graph.add_edge('B', 'C', -3)
    graph.add_edge('C', 'D', 2)

    # Dijkstra không xử lý được trọng số âm
    # Bellman-Ford có thể xử lý
```

**Kết quả kiểm thử:**
- Thuật toán Dijkstra hoạt động chính xác với đồ thị có trọng số không âm
- Thuật toán Bellman-Ford xử lý được cả trọng số âm và phát hiện chu trình âm
- Giao diện trực quan giúp người dùng dễ hiểu kết quả
- Thời gian chạy phù hợp với đồ thị có kích thước vừa phải

---

## CHƯƠNG 4: KẾT QUẢ VÀ HƯỚNG PHÁT TRIỂN

### 4.1 Kết quả đạt được

**Về mặt lý thuyết:**
- Nghiên cứu và hiểu rõ 4 thuật toán tìm đường đi ngắn nhất cơ bản
- Phân tích ưu nhược điểm và độ phức tạp của từng thuật toán
- So sánh hiệu quả và phạm vi ứng dụng của các thuật toán

**Về mặt thực hành:**
- Cài đặt thành công thuật toán Dijkstra và Bellman-Ford bằng Python
- Xây dựng được chương trình demo với giao diện trực quan
- Kiểm thử và xác minh tính đúng đắn của thuật toán
- Tạo được các test case để đánh giá hiệu quả

**Kết quả cụ thể:**
- Chương trình chạy ổn định với các đồ thị có kích thước vừa phải
- Giao diện đồ họa giúp người dùng dễ hiểu thuật toán
- Có thể xử lý cả đồ thị có hướng và vô hướng
- Phát hiện và xử lý được các trường hợp đặc biệt

### 4.2 Hạn chế của đề tài

**Hạn chế về thuật toán:**
- Chưa cài đặt thuật toán Floyd-Warshall và SPFA
- Chưa tối ưu hóa hiệu suất cho đồ thị lớn
- Chưa xử lý các trường hợp đặc biệt phức tạp

**Hạn chế về giao diện:**
- Giao diện còn đơn giản, chưa có tính tương tác cao
- Chưa có chức năng nhập đồ thị từ file
- Chưa có chức năng lưu kết quả

**Hạn chế về tính năng:**
- Chưa có chức năng so sánh hiệu suất chi tiết
- Chưa có animation để minh họa quá trình thực hiện thuật toán
- Chưa hỗ trợ đồ thị có kích thước lớn

### 4.3 Hướng phát triển

**Ngắn hạn:**
1. **Hoàn thiện thuật toán:**
   - Cài đặt thêm Floyd-Warshall và SPFA
   - Tối ưu hóa hiệu suất với cấu trúc dữ liệu tốt hơn
   - Thêm các thuật toán heuristic như A*

2. **Cải thiện giao diện:**
   - Xây dựng GUI với tkinter hoặc PyQt
   - Thêm chức năng nhập/xuất đồ thị từ file
   - Tạo animation minh họa quá trình thực hiện

**Dài hạn:**
1. **Mở rộng ứng dụng:**
   - Ứng dụng vào bài toán thực tế (GPS, game, mạng)
   - Xử lý đồ thị địa lý với dữ liệu thực
   - Tích hợp với API bản đồ

2. **Nghiên cứu nâng cao:**
   - Thuật toán song song và phân tán
   - Thuật toán xấp xỉ cho đồ thị lớn
   - Machine learning trong tối ưu đường đi

3. **Phát triển sản phẩm:**
   - Web application với framework như Flask/Django
   - Mobile app cho navigation
   - API service cho các ứng dụng khác

---

## KẾT LUẬN

Qua quá trình thực hiện đồ án "Tìm đường đi ngắn nhất trên đồ thị có trọng số", em đã đạt được những kết quả sau:

**Về kiến thức:**
- Hiểu sâu về lý thuyết đồ thị và các thuật toán tìm đường đi ngắn nhất
- Nắm vững ưu nhược điểm và phạm vi ứng dụng của từng thuật toán
- Có khả năng phân tích và so sánh hiệu quả các thuật toán

**Về kỹ năng:**
- Rèn luyện kỹ năng lập trình Python và sử dụng các thư viện
- Phát triển khả năng thiết kế và cài đặt thuật toán
- Học cách tạo giao diện trực quan để minh họa thuật toán

**Về ứng dụng:**
- Nhận thức được tầm quan trọng của bài toán trong thực tế
- Hiểu được cách áp dụng lý thuyết vào giải quyết vấn đề cụ thể
- Có nền tảng để phát triển các ứng dụng phức tạp hơn

Đồ án đã giúp em củng cố kiến thức về cấu trúc dữ liệu và giải thuật, đồng thời phát triển kỹ năng lập trình và tư duy giải quyết vấn đề. Đây là nền tảng quan trọng cho việc học tập và nghiên cứu các chủ đề nâng cao hơn trong tương lai.

---

## TÀI LIỆU THAM KHẢO

1. **Sách giáo khoa:**
   - Cormen, T. H., Leiserson, C. E., Rivest, R. L., & Stein, C. (2009). *Introduction to Algorithms* (3rd ed.). MIT Press.
   - Sedgewick, R., & Wayne, K. (2011). *Algorithms* (4th ed.). Addison-Wesley.

2. **Tài liệu trực tuyến:**
   - GeeksforGeeks. "Dijkstra's shortest path algorithm". https://www.geeksforgeeks.org/dijkstras-shortest-path-algorithm-greedy-algo-7/
   - Wikipedia. "Shortest path problem". https://en.wikipedia.org/wiki/Shortest_path_problem

3. **Khóa học:**
   - MIT OpenCourseWare. "Introduction to Algorithms"
   - Coursera. "Algorithms Specialization" by Stanford University

4. **Thư viện Python:**
   - NetworkX Documentation. https://networkx.org/
   - Matplotlib Documentation. https://matplotlib.org/

---

## PHỤ LỤC

### Phụ lục A: Mã nguồn đầy đủ

```python
# File: shortest_path.py
# Chương trình demo thuật toán tìm đường đi ngắn nhất

import heapq
import sys
from collections import defaultdict, deque
import matplotlib.pyplot as plt
import networkx as nx

class Graph:
    def __init__(self):
        self.vertices = set()
        self.edges = defaultdict(list)

    def add_edge(self, u, v, weight):
        """Thêm cạnh có trọng số vào đồ thị"""
        self.vertices.add(u)
        self.vertices.add(v)
        self.edges[u].append((v, weight))

    def add_undirected_edge(self, u, v, weight):
        """Thêm cạnh vô hướng"""
        self.add_edge(u, v, weight)
        self.add_edge(v, u, weight)

    def dijkstra(self, start, end=None):
        """Thuật toán Dijkstra"""
        distances = {vertex: float('infinity') for vertex in self.vertices}
        previous = {vertex: None for vertex in self.vertices}
        distances[start] = 0

        pq = [(0, start)]
        visited = set()

        while pq:
            current_distance, current_vertex = heapq.heappop(pq)

            if current_vertex in visited:
                continue

            visited.add(current_vertex)

            if end and current_vertex == end:
                break

            for neighbor, weight in self.edges[current_vertex]:
                if neighbor not in visited:
                    new_distance = current_distance + weight

                    if new_distance < distances[neighbor]:
                        distances[neighbor] = new_distance
                        previous[neighbor] = current_vertex
                        heapq.heappush(pq, (new_distance, neighbor))

        return distances, previous

    def bellman_ford(self, start):
        """Thuật toán Bellman-Ford"""
        distances = {vertex: float('infinity') for vertex in self.vertices}
        previous = {vertex: None for vertex in self.vertices}
        distances[start] = 0

        # Relaxation V-1 lần
        for _ in range(len(self.vertices) - 1):
            for u in self.vertices:
                for v, weight in self.edges[u]:
                    if distances[u] != float('infinity') and distances[u] + weight < distances[v]:
                        distances[v] = distances[u] + weight
                        previous[v] = u

        # Kiểm tra chu trình âm
        has_negative_cycle = False
        for u in self.vertices:
            for v, weight in self.edges[u]:
                if distances[u] != float('infinity') and distances[u] + weight < distances[v]:
                    has_negative_cycle = True
                    break
            if has_negative_cycle:
                break

        return distances, previous, has_negative_cycle

    def get_path(self, previous, start, end):
        """Tái tạo đường đi"""
        path = []
        current = end

        while current is not None:
            path.append(current)
            current = previous[current]

        path.reverse()

        if path[0] != start:
            return None

        return path

class ShortestPathVisualizer:
    def __init__(self):
        self.graph = Graph()

    def create_sample_graph(self):
        """Tạo đồ thị mẫu"""
        edges = [
            ('A', 'B', 4), ('A', 'C', 2),
            ('B', 'C', 1), ('B', 'D', 5),
            ('C', 'D', 8), ('C', 'E', 10),
            ('D', 'E', 2), ('D', 'F', 6),
            ('E', 'F', 3)
        ]

        for u, v, w in edges:
            self.graph.add_undirected_edge(u, v, w)

    def visualize_graph(self, path=None, title="Đồ thị"):
        """Vẽ đồ thị"""
        G = nx.Graph()

        for u in self.graph.vertices:
            for v, weight in self.graph.edges[u]:
                if not G.has_edge(u, v):
                    G.add_edge(u, v, weight=weight)

        plt.figure(figsize=(12, 8))
        pos = nx.spring_layout(G, seed=42)

        nx.draw_networkx_edges(G, pos, alpha=0.5, width=1)

        if path:
            path_edges = [(path[i], path[i+1]) for i in range(len(path)-1)]
            nx.draw_networkx_edges(G, pos, edgelist=path_edges,
                                 edge_color='red', width=3)

        nx.draw_networkx_nodes(G, pos, node_color='lightblue',
                              node_size=1000, alpha=0.9)
        nx.draw_networkx_labels(G, pos, font_size=16, font_weight='bold')

        edge_labels = nx.get_edge_attributes(G, 'weight')
        nx.draw_networkx_edge_labels(G, pos, edge_labels, font_size=12)

        plt.title(title, fontsize=16, fontweight='bold')
        plt.axis('off')
        plt.tight_layout()
        plt.show()

    def run_demo(self):
        """Chạy demo"""
        print("=== DEMO THUẬT TOÁN TÌM ĐƯỜNG ĐI NGẮN NHẤT ===\n")

        self.create_sample_graph()

        print("Đồ thị mẫu đã được tạo với các đỉnh:",
              sorted(list(self.graph.vertices)))
        print("\nCác cạnh trong đồ thị:")
        for u in sorted(self.graph.vertices):
            for v, weight in self.graph.edges[u]:
                if u < v:
                    print(f"  {u} -- {v}: {weight}")

        self.visualize_graph(title="Đồ thị ban đầu")

        start = input("\nNhập đỉnh bắt đầu: ").strip().upper()
        end = input("Nhập đỉnh kết thúc: ").strip().upper()

        if start not in self.graph.vertices or end not in self.graph.vertices:
            print("Đỉnh không tồn tại trong đồ thị!")
            return

        print(f"\n=== THUẬT TOÁN DIJKSTRA ===")
        distances, previous = self.graph.dijkstra(start, end)
        path = self.graph.get_path(previous, start, end)

        if path:
            print(f"Đường đi ngắn nhất từ {start} đến {end}: {' -> '.join(path)}")
            print(f"Độ dài đường đi: {distances[end]}")

            self.visualize_graph(path, f"Đường đi ngắn nhất từ {start} đến {end} (Dijkstra)")
        else:
            print(f"Không có đường đi từ {start} đến {end}")

        print(f"\n=== THUẬT TOÁN BELLMAN-FORD ===")
        bf_distances, bf_previous, has_negative_cycle = self.graph.bellman_ford(start)

        if has_negative_cycle:
            print("Phát hiện chu trình âm trong đồ thị!")
        else:
            bf_path = self.graph.get_path(bf_previous, start, end)
            if bf_path:
                print(f"Đường đi ngắn nhất từ {start} đến {end}: {' -> '.join(bf_path)}")
                print(f"Độ dài đường đi: {bf_distances[end]}")

                if distances[end] == bf_distances[end]:
                    print("✓ Kết quả của hai thuật toán giống nhau")
                else:
                    print("✗ Kết quả của hai thuật toán khác nhau")

if __name__ == "__main__":
    visualizer = ShortestPathVisualizer()
    visualizer.run_demo()
```

### Phụ lục B: Kết quả chạy thử

```
=== DEMO THUẬT TOÁN TÌM ĐƯỜNG ĐI NGẮN NHẤT ===

Đồ thị mẫu đã được tạo với các đỉnh: ['A', 'B', 'C', 'D', 'E', 'F']

Các cạnh trong đồ thị:
  A -- B: 4
  A -- C: 2
  B -- C: 1
  B -- D: 5
  C -- D: 8
  C -- E: 10
  D -- E: 2
  D -- F: 6
  E -- F: 3

Nhập đỉnh bắt đầu: A
Nhập đỉnh kết thúc: F

=== THUẬT TOÁN DIJKSTRA ===
Đường đi ngắn nhất từ A đến F: A -> C -> B -> D -> E -> F
Độ dài đường đi: 12

=== THUẬT TOÁN BELLMAN-FORD ===
Đường đi ngắn nhất từ A đến F: A -> C -> B -> D -> E -> F
Độ dài đường đi: 12
✓ Kết quả của hai thuật toán giống nhau
```

---

**Ghi chú:** Báo cáo này được viết với mục đích học tập và nghiên cứu. Mã nguồn có thể được cải thiện thêm để tối ưu hiệu suất và thêm các tính năng mới.
```
