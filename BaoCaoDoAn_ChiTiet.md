# BÁO CÁO ĐỒ ÁN
## TÌM ĐƯỜNG ĐI NGẮN NHẤT TRÊN ĐỒ THỊ CÓ TRỌNG SỐ

**Sinh viên thực hiện:** [Họ và tên sinh viên]  
**Mã số sinh viên:** [MSSV]  
**Lớp:** [Tên lớp]  
**Khoa:** Công nghệ thông tin  
**Giảng viên hướng dẫn:** [Tên giảng viên]  
**Năm học:** 2024-2025

---

## MỤC LỤC

**LỜI CẢM ƠN** .................................................... 3

**CHƯƠNG 1: TỔNG QUAN ĐỀ TÀI** ................................. 4
- 1.1 Giới thiệu chung .......................................... 4
- 1.2 Đặt vấn đề ................................................ 5
- 1.3 Mục tiêu và phạm vi đề tài ................................ 6

**CHƯƠNG 2: NGHIÊN CỨU LÝ THUYẾT** ............................. 7
- 2.1 Khái niệm cơ bản về đồ thị ................................ 7
- 2.2 Bài toán đường đi ngắn nhất ............................... 9
- 2.3 Các thuật toán tìm đường đi ngắn nhất ..................... 11
  - 2.3.1 Thuật toán Dijkstra .................................. 11
  - 2.3.2 Thuật toán Bellman-Ford .............................. 14
  - 2.3.3 Thuật toán Floyd-Warshall ............................ 17
  - 2.3.4 Thuật toán SPFA ...................................... 20

**CHƯƠNG 3: XÂY DỰNG CHƯƠNG TRÌNH THUẬT TOÁN** ................ 22
- 3.1 Lựa chọn thuật toán ....................................... 22
- 3.2 Cấu trúc dữ liệu sử dụng ................................. 23
- 3.3 Chi tiết cài đặt thuật toán ............................... 24
- 3.4 Giao diện chương trình và hướng dẫn sử dụng .............. 28
- 3.5 Kiểm thử và đánh giá ...................................... 30

**CHƯƠNG 4: KẾT QUẢ VÀ HƯỚNG PHÁT TRIỂN** ..................... 32
- 4.1 Kết quả đạt được .......................................... 32
- 4.2 Hạn chế của đề tài ........................................ 33
- 4.3 Hướng phát triển .......................................... 34

**KẾT LUẬN** ...................................................... 35

**TÀI LIỆU THAM KHẢO** ............................................ 36

**PHỤ LỤC** ....................................................... 37

---

## LỜI CẢM ƠN

Em xin chân thành cảm ơn thầy/cô [Tên giảng viên] đã tận tình hướng dẫn, chỉ bảo và tạo điều kiện thuận lợi cho em trong suốt quá trình thực hiện đồ án này. Những kiến thức chuyên môn sâu sắc, kinh nghiệm thực tế quý báu và sự nhiệt tình của thầy/cô đã giúp em hoàn thành đồ án một cách tốt nhất.

Em cũng xin gửi lời cảm ơn đến các thầy cô trong khoa Công nghệ thông tin đã truyền đạt những kiến thức nền tảng vững chắc về cấu trúc dữ liệu và giải thuật, tạo cơ sở để em có thể thực hiện đồ án này.

Đặc biệt, em xin cảm ơn gia đình, bạn bè đã luôn động viên, khuyến khích và tạo điều kiện tốt nhất để em có thể tập trung học tập và nghiên cứu.

Mặc dù đã rất cố gắng, nhưng do hạn chế về thời gian và kinh nghiệm, đồ án không tránh khỏi những thiếu sót. Em rất mong nhận được sự góp ý, chỉ bảo của thầy cô và các bạn để đồ án được hoàn thiện hơn.

Em xin chân thành cảm ơn!

---

## CHƯƠNG 1: TỔNG QUAN ĐỀ TÀI

### 1.1 Giới thiệu chung

Trong thời đại công nghệ thông tin phát triển mạnh mẽ như hiện nay, việc tối ưu hóa các thuật toán để giải quyết các bài toán thực tế đang trở thành một nhu cầu cấp thiết. Một trong những bài toán cơ bản và quan trọng nhất trong lý thuyết đồ thị và khoa học máy tính là bài toán tìm đường đi ngắn nhất trên đồ thị có trọng số.

Bài toán tìm đường đi ngắn nhất có lịch sử phát triển lâu đời và được ứng dụng rộng rãi trong nhiều lĩnh vực khác nhau:

**Trong giao thông vận tải:**
- Hệ thống định vị GPS (Global Positioning System) sử dụng các thuật toán tìm đường đi ngắn nhất để tính toán tuyến đường tối ưu cho người dùng
- Các ứng dụng như Google Maps, Waze, HERE Maps đều dựa trên các thuật toán này
- Tối ưu hóa lộ trình vận chuyển hàng hóa, giảm thiểu chi phí nhiên liệu và thời gian

**Trong mạng máy tính:**
- Định tuyến gói tin trong mạng Internet thông qua các giao thức như OSPF (Open Shortest Path First), RIP (Routing Information Protocol)
- Tối ưu hóa băng thông mạng và giảm độ trễ truyền dữ liệu
- Thiết kế topology mạng hiệu quả

**Trong trò chơi điện tử:**
- AI pathfinding cho các nhân vật trong game
- Tối ưu hóa di chuyển của các đối tượng trong môi trường ảo
- Xây dựng hệ thống navigation mesh

**Trong logistics và chuỗi cung ứng:**
- Tối ưu hóa tuyến đường giao hàng
- Quản lý kho bãi và phân phối hàng hóa
- Giảm thiểu chi phí vận chuyển

**Trong tài chính và kinh tế:**
- Tối ưu hóa danh mục đầu tư
- Phân tích rủi ro và lợi nhuận
- Mô hình hóa các mối quan hệ kinh tế

Việc nghiên cứu và hiểu rõ các thuật toán tìm đường đi ngắn nhất không chỉ có ý nghĩa học thuật mà còn có giá trị thực tiễn cao, giúp giải quyết nhiều bài toán phức tạp trong cuộc sống.

### 1.2 Đặt vấn đề

Trong thực tế, chúng ta thường xuyên gặp phải các tình huống cần tìm ra con đường tối ưu nhất để đi từ điểm A đến điểm B. Tuy nhiên, khái niệm "tối ưu" có thể được hiểu theo nhiều cách khác nhau:

**Về mặt khoảng cách:**
- Tìm đường đi có tổng khoảng cách ngắn nhất
- Ví dụ: Đi từ nhà đến trường theo con đường có tổng số km ít nhất

**Về mặt thời gian:**
- Tìm đường đi có thời gian di chuyển ít nhất
- Ví dụ: Chọn tuyến đường tránh kẹt xe để đến nơi nhanh nhất

**Về mặt chi phí:**
- Tìm đường đi có tổng chi phí thấp nhất
- Ví dụ: Chọn tuyến bay có giá vé rẻ nhất (có thể phải transit nhiều lần)

**Về mặt an toàn:**
- Tìm đường đi có độ rủi ro thấp nhất
- Ví dụ: Chọn tuyến đường ít tai nạn giao thông

**Các thách thức khi giải quyết bài toán:**

1. **Quy mô dữ liệu lớn:**
   - Đồ thị thực tế có thể có hàng triệu đỉnh và cạnh
   - Cần thuật toán hiệu quả để xử lý trong thời gian hợp lý

2. **Dữ liệu động:**
   - Trọng số các cạnh có thể thay đổi theo thời gian (tình trạng giao thông, thời tiết)
   - Cần cập nhật kết quả nhanh chóng khi có thay đổi

3. **Ràng buộc phức tạp:**
   - Có thể có các ràng buộc về thời gian, loại phương tiện, điều kiện đường đi
   - Cần thuật toán linh hoạt để xử lý các ràng buộc này

4. **Trọng số âm:**
   - Trong một số trường hợp, có thể xuất hiện trọng số âm (ví dụ: được thưởng khi đi qua một điểm nào đó)
   - Cần thuật toán có khả năng xử lý trọng số âm và phát hiện chu trình âm

5. **Yêu cầu thời gian thực:**
   - Nhiều ứng dụng cần kết quả ngay lập tức (GPS navigation)
   - Cần cân bằng giữa độ chính xác và tốc độ tính toán

**Ví dụ cụ thể:**

Xét bài toán một công ty vận chuyển cần giao hàng từ kho chính đến các cửa hàng trong thành phố. Công ty có thể:
- Chọn đường ngắn nhất để tiết kiệm nhiên liệu
- Chọn đường nhanh nhất để giao hàng đúng giờ
- Chọn đường ít tắc nghẽn nhất để đảm bảo độ tin cậy
- Chọn đường có phí cầu đường thấp nhất để giảm chi phí

Mỗi lựa chọn sẽ dẫn đến một mô hình đồ thị khác nhau với các trọng số khác nhau, và cần các thuật toán phù hợp để giải quyết.

### 1.3 Mục tiêu và phạm vi đề tài

#### 1.3.1 Mục tiêu của đề tài

**Mục tiêu tổng quát:**
Nghiên cứu, tìm hiểu và cài đặt các thuật toán tìm đường đi ngắn nhất trên đồ thị có trọng số, từ đó xây dựng chương trình minh họa để hiểu rõ hoạt động và ứng dụng của các thuật toán này.

**Mục tiêu cụ thể:**

1. **Về mặt lý thuyết:**
   - Nghiên cứu sâu về lý thuyết đồ thị và các khái niệm liên quan
   - Tìm hiểu chi tiết 4 thuật toán chính: Dijkstra, Bellman-Ford, Floyd-Warshall, SPFA
   - Phân tích độ phức tạp thời gian và không gian của từng thuật toán
   - So sánh ưu nhược điểm và phạm vi ứng dụng của các thuật toán

2. **Về mặt thực hành:**
   - Cài đặt thành công ít nhất 2 thuật toán bằng ngôn ngữ Python
   - Xây dựng chương trình có giao diện trực quan để minh họa hoạt động của thuật toán
   - Tạo các test case đa dạng để kiểm thử tính đúng đắn của thuật toán
   - Đo lường và so sánh hiệu suất của các thuật toán

3. **Về mặt ứng dụng:**
   - Hiểu rõ cách áp dụng các thuật toán vào các bài toán thực tế
   - Xây dựng ví dụ minh họa cụ thể cho từng loại thuật toán
   - Đề xuất hướng phát triển và cải tiến cho các ứng dụng thực tế

#### 1.3.2 Phạm vi nghiên cứu

**Phạm vi về thuật toán:**
- Tập trung vào 4 thuật toán cơ bản và quan trọng nhất:
  - Dijkstra's algorithm (thuật toán Dijkstra)
  - Bellman-Ford algorithm (thuật toán Bellman-Ford)
  - Floyd-Warshall algorithm (thuật toán Floyd-Warshall)
  - SPFA - Shortest Path Faster Algorithm

**Phạm vi về loại đồ thị:**
- Đồ thị có hướng và vô hướng
- Đồ thị có trọng số dương và âm
- Đồ thị liên thông và không liên thông
- Đồ thị có chu trình và không có chu trình

**Phạm vi về cài đặt:**
- Sử dụng ngôn ngữ lập trình Python làm công cụ chính
- Cài đặt chi tiết thuật toán Dijkstra và Bellman-Ford
- Mô tả thuật toán Floyd-Warshall và SPFA (không cài đặt chi tiết)
- Sử dụng các thư viện hỗ trợ: matplotlib, networkx cho visualization

**Phạm vi về kiểm thử:**
- Tạo các đồ thị test với kích thước nhỏ và vừa (dưới 100 đỉnh)
- Kiểm thử với các trường hợp đặc biệt: đồ thị có chu trình âm, đồ thị không liên thông
- So sánh kết quả giữa các thuật toán trên cùng một đồ thị
- Đo lường thời gian thực hiện và bộ nhớ sử dụng

#### 1.3.3 Giới hạn của đề tài

**Giới hạn về quy mô:**
- Không xử lý đồ thị có quy mô rất lớn (hàng triệu đỉnh)
- Không tối ưu hóa cho các hệ thống phân tán hoặc song song

**Giới hạn về thuật toán:**
- Không nghiên cứu các thuật toán nâng cao như A*, Bidirectional search
- Không xem xét các thuật toán xấp xỉ hoặc heuristic

**Giới hạn về ứng dụng:**
- Không xây dựng ứng dụng thương mại hoàn chỉnh
- Không tích hợp với dữ liệu thực tế từ các API bên ngoài

**Giới hạn về giao diện:**
- Giao diện đơn giản, chủ yếu dùng để minh họa và demo
- Không phát triển giao diện web hoặc mobile

---

## CHƯƠNG 2: NGHIÊN CỨU LÝ THUYẾT

### 2.1 Khái niệm cơ bản về đồ thị

#### 2.1.1 Định nghĩa đồ thị

**Định nghĩa:** Đồ thị G là một cặp có thứ tự G = (V, E), trong đó:
- V là tập hợp hữu hạn các đỉnh (vertices hoặc nodes)
- E là tập hợp các cạnh (edges) nối các đỉnh với nhau

**Ký hiệu toán học:**
- |V| = n: số lượng đỉnh trong đồ thị
- |E| = m: số lượng cạnh trong đồ thị
- Với đồ thị đơn giản: m ≤ n(n-1)/2 (đồ thị vô hướng) hoặc m ≤ n(n-1) (đồ thị có hướng)

#### 2.1.2 Phân loại đồ thị

**1. Đồ thị có hướng (Directed Graph - Digraph):**
- Các cạnh có hướng, được biểu diễn bằng mũi tên
- Cạnh (u,v) khác với cạnh (v,u)
- Ứng dụng: mô hình hóa các mối quan hệ một chiều như đường phố một chiều, quan hệ phụ thuộc

**2. Đồ thị vô hướng (Undirected Graph):**
- Các cạnh không có hướng
- Cạnh (u,v) tương đương với cạnh (v,u)
- Ứng dụng: mô hình hóa các mối quan hệ hai chiều như đường phố hai chiều, mạng xã hội

**3. Đồ thị có trọng số (Weighted Graph):**
- Mỗi cạnh được gán một giá trị số gọi là trọng số w(u,v)
- Trọng số có thể biểu diễn khoảng cách, chi phí, thời gian, v.v.
- Có thể có trọng số dương, âm hoặc bằng 0

**4. Đồ thị đơn giản (Simple Graph):**
- Không có cạnh lặp (multiple edges)
- Không có khuyên (self-loops)

#### 2.1.3 Các khái niệm liên quan

**1. Đường đi (Path):**
- Là một dãy các đỉnh v₁, v₂, ..., vₖ sao cho tồn tại cạnh (vᵢ, vᵢ₊₁) với mọi i = 1, 2, ..., k-1
- Độ dài đường đi: số cạnh trong đường đi (k-1)
- Đường đi đơn giản: không có đỉnh nào được lặp lại

**2. Chu trình (Cycle):**
- Là một đường đi khép kín, bắt đầu và kết thúc tại cùng một đỉnh
- Chu trình đơn giản: không có đỉnh nào được lặp lại (trừ đỉnh đầu và cuối)

**3. Đồ thị liên thông (Connected Graph):**
- Đồ thị vô hướng: tồn tại đường đi giữa mọi cặp đỉnh
- Đồ thị có hướng:
  - Liên thông mạnh: tồn tại đường đi có hướng giữa mọi cặp đỉnh
  - Liên thông yếu: đồ thị vô hướng tương ứng là liên thông

**4. Bậc của đỉnh (Degree):**
- Đồ thị vô hướng: số cạnh kề với đỉnh đó
- Đồ thị có hướng:
  - Bậc vào (in-degree): số cạnh đi vào đỉnh
  - Bậc ra (out-degree): số cạnh đi ra từ đỉnh

#### 2.1.4 Biểu diễn đồ thị

**1. Ma trận kề (Adjacency Matrix):**
- Ma trận A có kích thước n×n
- A[i][j] = w(i,j) nếu có cạnh từ i đến j với trọng số w(i,j)
- A[i][j] = ∞ (hoặc giá trị đặc biệt) nếu không có cạnh

*Ưu điểm:*
- Kiểm tra sự tồn tại của cạnh trong O(1)
- Dễ cài đặt và hiểu

*Nhược điểm:*
- Tốn O(n²) bộ nhớ
- Không hiệu quả với đồ thị thưa (sparse graph)

**2. Danh sách kề (Adjacency List):**
- Mỗi đỉnh u có một danh sách chứa các đỉnh kề với u
- Với đồ thị có trọng số: lưu cặp (đỉnh, trọng số)

*Ưu điểm:*
- Tiết kiệm bộ nhớ: O(n + m)
- Hiệu quả với đồ thị thưa
- Duyệt các đỉnh kề nhanh chóng

*Nhược điểm:*
- Kiểm tra sự tồn tại của cạnh có thể tốn O(n) trong trường hợp xấu nhất

**3. Danh sách cạnh (Edge List):**
- Lưu trữ tất cả các cạnh trong một danh sách
- Mỗi cạnh được biểu diễn bằng bộ ba (u, v, w)

*Ưu điểm:*
- Đơn giản, dễ cài đặt
- Tiết kiệm bộ nhớ: O(m)

*Nhược điểm:*
- Không hiệu quả cho việc tìm kiếm đỉnh kề
- Thường được sử dụng trong các thuật toán cần duyệt tất cả các cạnh

#### 2.1.5 Ví dụ minh họa

Xét đồ thị có hướng có trọng số với 4 đỉnh A, B, C, D:
- Cạnh A→B với trọng số 5
- Cạnh A→C với trọng số 3
- Cạnh B→C với trọng số 2
- Cạnh B→D với trọng số 4
- Cạnh C→D với trọng số 1

**Biểu diễn bằng ma trận kề:**
```
    A  B  C  D
A [ 0  5  3  ∞]
B [ ∞  0  2  4]
C [ ∞  ∞  0  1]
D [ ∞  ∞  ∞  0]
```

**Biểu diễn bằng danh sách kề:**
```
A: [(B,5), (C,3)]
B: [(C,2), (D,4)]
C: [(D,1)]
D: []
```

### 2.2 Bài toán đường đi ngắn nhất

#### 2.2.1 Định nghĩa bài toán

**Bài toán tổng quát:**
Cho đồ thị có trọng số G = (V, E) với hàm trọng số w: E → ℝ. Tìm đường đi từ đỉnh nguồn s đến đỉnh đích t sao cho tổng trọng số của các cạnh trên đường đi là nhỏ nhất.

**Định nghĩa chính thức:**
- Cho đường đi P = ⟨v₀, v₁, v₂, ..., vₖ⟩ từ v₀ = s đến vₖ = t
- Trọng số của đường đi: w(P) = Σᵢ₌₀ᵏ⁻¹ w(vᵢ, vᵢ₊₁)
- Khoảng cách ngắn nhất từ s đến t: δ(s,t) = min{w(P) : P là đường đi từ s đến t}

**Tính chất quan trọng:**
1. **Tính chất con đường tối ưu (Optimal Substructure):**
   - Nếu P là đường đi ngắn nhất từ s đến t, và P đi qua đỉnh u, thì đoạn đường từ s đến u và từ u đến t cũng là ngắn nhất
   - Đây là cơ sở cho việc áp dụng quy hoạch động

2. **Bất đẳng thức tam giác:**
   - δ(s,t) ≤ δ(s,u) + δ(u,t) với mọi đỉnh u
   - Đường đi trực tiếp không dài hơn đường đi qua trung gian

#### 2.2.2 Các biến thể của bài toán

**1. Single-Source Shortest Path (SSSP):**
- **Mô tả:** Tìm đường đi ngắn nhất từ một đỉnh nguồn s đến tất cả các đỉnh khác
- **Input:** Đồ thị G = (V,E), đỉnh nguồn s
- **Output:** Mảng khoảng cách d[v] = δ(s,v) với mọi v ∈ V
- **Thuật toán:** Dijkstra, Bellman-Ford, SPFA
- **Ứng dụng:** GPS navigation từ vị trí hiện tại đến tất cả địa điểm quan tâm

**2. Single-Destination Shortest Path:**
- **Mô tả:** Tìm đường đi ngắn nhất từ tất cả các đỉnh đến một đỉnh đích t
- **Giải pháp:** Chuyển về bài toán SSSP bằng cách đảo ngược hướng các cạnh
- **Ứng dụng:** Tìm đường đi tối ưu đến một trung tâm phân phối

**3. Single-Pair Shortest Path:**
- **Mô tả:** Tìm đường đi ngắn nhất giữa hai đỉnh cụ thể s và t
- **Lưu ý:** Không có thuật toán nào tốt hơn asymptotically so với SSSP
- **Tối ưu hóa:** Có thể dừng sớm khi tìm thấy đích (như trong Dijkstra)
- **Ứng dụng:** Tìm đường đi từ A đến B cụ thể

**4. All-Pairs Shortest Path (APSP):**
- **Mô tả:** Tìm đường đi ngắn nhất giữa mọi cặp đỉnh
- **Input:** Đồ thị G = (V,E)
- **Output:** Ma trận khoảng cách D[u][v] = δ(u,v) với mọi u,v ∈ V
- **Thuật toán:** Floyd-Warshall, Johnson's algorithm
- **Ứng dụng:** Tính toán ma trận khoảng cách cho toàn bộ mạng lưới

#### 2.2.3 Các trường hợp đặc biệt

**1. Đồ thị không có trọng số (Unweighted Graph):**
- Mọi cạnh có trọng số bằng 1
- Có thể sử dụng BFS (Breadth-First Search) với độ phức tạp O(V + E)
- Đơn giản và hiệu quả nhất cho trường hợp này

**2. Đồ thị có trọng số không âm:**
- w(e) ≥ 0 với mọi cạnh e
- Có thể sử dụng thuật toán Dijkstra
- Đảm bảo tính tham lam (greedy choice) là tối ưu

**3. Đồ thị có trọng số âm:**
- Một số cạnh có trọng số âm
- Không thể sử dụng Dijkstra
- Cần sử dụng Bellman-Ford hoặc SPFA
- Phải kiểm tra chu trình âm

**4. Đồ thị có chu trình âm:**
- Tồn tại chu trình có tổng trọng số âm
- Không tồn tại đường đi ngắn nhất (có thể giảm vô hạn)
- Cần phát hiện và xử lý đặc biệt

#### 2.2.4 Ứng dụng thực tế

**1. Hệ thống định vị và dẫn đường:**
- **Bài toán:** Tìm tuyến đường từ vị trí hiện tại đến đích
- **Mô hình:** Đỉnh là giao lộ, cạnh là đoạn đường, trọng số là thời gian/khoảng cách
- **Thách thức:** Dữ liệu thay đổi theo thời gian thực (tắc đường, tai nạn)

**2. Mạng máy tính và Internet:**
- **Bài toán:** Định tuyến gói tin từ nguồn đến đích
- **Mô hình:** Đỉnh là router, cạnh là liên kết mạng, trọng số là độ trễ/băng thông
- **Giao thức:** OSPF, RIP, BGP

**3. Logistics và vận chuyển:**
- **Bài toán:** Tối ưu hóa tuyến đường giao hàng
- **Mô hình:** Đỉnh là kho/cửa hàng, cạnh là tuyến đường, trọng số là chi phí/thời gian
- **Mở rộng:** Vehicle Routing Problem (VRP)

**4. Trò chơi và AI:**
- **Bài toán:** Pathfinding cho nhân vật trong game
- **Mô hình:** Đỉnh là vị trí, cạnh là di chuyển có thể, trọng số là chi phí di chuyển
- **Thuật toán:** A* (kết hợp Dijkstra với heuristic)

**5. Mạng xã hội:**
- **Bài toán:** Tìm mối liên hệ ngắn nhất giữa hai người
- **Mô hình:** Đỉnh là người, cạnh là mối quan hệ, trọng số là "độ gần gũi"
- **Ứng dụng:** Gợi ý kết bạn, phân tích ảnh hưởng
